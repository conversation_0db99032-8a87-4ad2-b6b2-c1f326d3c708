
import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { store } from '../store/store';
import { X, Plus, Mic, MessageCircle, Image, FileText, Code, GraduationCap, MoreHorizontal } from 'lucide-react';
import ChatArea from './ChatArea';
import ChatInput from './ChatInput';

const FloatingChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);

  const suggestions = [
    { icon: <Image className="w-5 h-5" />, text: "Create image", color: "text-green-600" },
    { icon: <FileText className="w-5 h-5" />, text: "Summarize text", color: "text-orange-600" },
    { icon: <Code className="w-5 h-5" />, text: "Code", color: "text-purple-600" },
    { icon: <GraduationCap className="w-5 h-5" />, text: "Get advice", color: "text-blue-600" },
    { icon: <MoreHorizontal className="w-5 h-5" />, text: "More", color: "text-gray-600" },
  ];

  if (isMinimized) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => {
            setIsMinimized(false);
            setIsOpen(true);
          }}
          className="w-14 h-14 bg-black hover:bg-gray-800 text-white rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-110 relative group"
        >
          <MessageCircle className="w-7 h-7" />
          
          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat with AI
          </div>
        </button>
      </div>
    );
  }

  return (
    <Provider store={store}>
      <div className="fixed bottom-6 right-6 z-50">
        <div className="w-96 h-[600px] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
          
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-white" />
              </div>
              <span className="font-medium text-gray-900 dark:text-white">AI Assistant</span>
            </div>
            
            <button
              onClick={() => setIsMinimized(true)}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Minimize"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>

          {/* Content */}
          {!isOpen ? (
            // Welcome Screen
            <div className="flex-1 flex flex-col items-center justify-center p-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                  What can I help with?
                </h2>
              </div>
              
              {/* Suggestion Buttons */}
              <div className="grid grid-cols-2 gap-3 mb-8 w-full">
                {suggestions.slice(0, 4).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setIsOpen(true)}
                    className="flex items-center space-x-3 p-4 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                  >
                    <span className={suggestion.color}>{suggestion.icon}</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {suggestion.text}
                    </span>
                  </button>
                ))}
              </div>
              
              {/* More Button */}
              <button
                onClick={() => setIsOpen(true)}
                className="flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <span className="text-gray-600">{suggestions[4].icon}</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {suggestions[4].text}
                </span>
              </button>

              {/* Input */}
              <div className="w-full mt-8">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Ask anything"
                    onClick={() => setIsOpen(true)}
                    className="w-full rounded-full border border-gray-300 dark:border-gray-600 px-4 py-3 pr-20 focus:border-blue-500 focus:outline-none dark:bg-gray-800 dark:text-white cursor-pointer"
                    readOnly
                  />
                  
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                    <button
                      onClick={() => setIsOpen(true)}
                      className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <Plus className="w-4 h-4 text-gray-500" />
                    </button>
                    
                    <button
                      onClick={() => setIsOpen(true)}
                      className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <Mic className="w-4 h-4 text-gray-500" />
                    </button>
                    
                    <button
                      onClick={() => setIsOpen(true)}
                      className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Chat Interface
            <div className="flex-1 flex flex-col">
              <div className="flex-1 overflow-hidden">
                <ChatArea />
              </div>
              <ChatInput />
            </div>
          )}
        </div>
      </div>
    </Provider>
  );
};

export default FloatingChatWidget;
