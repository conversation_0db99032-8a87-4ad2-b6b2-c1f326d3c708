// Self-executing function to avoid global namespace pollution
(function () {
    // Create required script tags
    const loadScript = (src) => {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    };

    // Create required style tags
    const loadStyle = (src) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = src;
        document.head.appendChild(link);
    };

    // Config from data attributes
    const scriptTag = document.currentScript;
    const config = {
        element: scriptTag?.getAttribute('data-element') || 'support-chat-widget-container',
        token: scriptTag?.getAttribute('data-support-token') || '',
        companyName: scriptTag?.getAttribute('data-company-name') || 'Max',
        project: scriptTag?.getAttribute('data-project-name') || 'Project1',
        // agentName: scriptTag?.getAttribute('data-agent-name') || 'Support Agent'
    };

    // Path to the assets (adjust to your server path)
    const basePath = scriptTag?.getAttribute('src')?.substring(0, scriptTag?.getAttribute('src')?.lastIndexOf('/'));

    // Load all required files
    Promise.all([
        loadScript(`${basePath}/react.production.min.js`),
        loadScript(`${basePath}/react-dom.production.min.js`)
    ])
        .then(() => loadScript(`${basePath}/support-chat-widget.umd.cjs`))
        .then(() => {
            loadStyle(`${basePath}/support-chat-widget.css`);
            // Initialize widget
            window?.initChatWidget(config);
        })
        .catch(err => console.error('Failed to load support chat widget:', err));
})();