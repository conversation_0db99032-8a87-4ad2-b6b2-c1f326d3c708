// import React from 'react'
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setSessionId, setToken } from '../store/slices/authSlice';
// import { RootState } from '../store';
import { AUTH } from '../config/endpoints.config';
import { useNavigate, useParams } from 'react-router-dom';
import { routes } from '../config/routes';
import toast from 'react-hot-toast';

const VerifySession: React.FC = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { key } = useParams();

    useEffect(() => {
        const verifySession = async (key: string) => {
            try {
                const response = await fetch(AUTH, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        "key": key,
                        "user_name": "9273673675",
                        "name": "Test User",
                        "phone": "9273673675",
                        "email": "<EMAIL>",
                        "project": "MaxCare",
                        "metadata": {
                            "source": "web",
                            "browser": "chrome"
                        }
                    }),
                });

                if (response.ok) {
                    const res = await response.json()
                    dispatch(setSessionId(res.data.sessionId))
                    dispatch(setToken(res.data.token))
                    navigate(routes.home)
                }
                toast.success('Session initialized')
            } catch (error) {
                toast.error('Failed to validate the session')
                console.error('Failed to request human support:', error);
            }
        };
        verifySession('' + key);
    }, [dispatch, navigate, key]);

    return (<></>)
}

export default VerifySession