
import React, { useEffect } from 'react';
import ChatWidget from '../components/ChatWidget';
import { WidgetConfig } from '../types/chat';
import { useSearchParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setDetails } from '@/store/slices/commonSlice';

const Widget: React.FC = () => {
  const [params] = useSearchParams();
  const dispatch = useDispatch();
  const agent = params.get('agent') || 'service';
  const token = params.get('token') || '';

  // Example configuration
  const config: WidgetConfig = {
    projectId: 'demo-project',
    userId: 'demo-user',
    apiKey: token || 'demo-api-key',
    botName: 'GoBot',
    agentType: agent,
    theme: {
      mode: 'light',
      primaryColor: '#3B82F6',
      backgroundColor: '#FFFFFF'
    }
  };

  useEffect(() => {
    const setAgentDetails = (token: string, agent: string) => {
      dispatch(setDetails({ token, type: agent }));
    };
    setAgentDetails(token, agent);
  }, [dispatch, agent, token]);

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Chat Widget Demo
        </h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Welcome to Max Mobility
          </h2>
          {/* <p className="text-gray-600 dark:text-gray-400 mb-6">
            This is a demo page showing the chat widget in action. The widget appears as a floating chat bubble
            in the bottom-right corner and can be expanded to full screen mode.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Features</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Floating chat bubble</li>
                <li>• Expandable to full screen</li>
                <li>• Real-time messaging</li>
                <li>• Dark/light mode support</li>
              </ul>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">How to Use</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Click the chat bubble to open</li>
                <li>• Use expand button for full screen</li>
                <li>• Type your message and press enter</li>
                <li>• Close or minimize as needed</li>
              </ul>
            </div>
          </div> */}
        </div>
      </div>

      {/* Chat Widget */}
      <ChatWidget config={config} />
    </div>
  );
};

export default Widget;
