
import { WidgetConfig } from '../types/chat';

export const createChatWidget = (config: WidgetConfig) => {
  // This function will be called when the widget script is loaded
  const initWidget = () => {
    const widgetContainer = document.createElement('div');
    widgetContainer.id = 'gobot-widget-container';
    document.body.appendChild(widgetContainer);

    // Dynamically import and render the widget
    import('../components/ChatWidget').then(({ default: ChatWidget }) => {
      import('react-dom/client').then(({ createRoot }) => {
        import('react').then((React) => {
          const root = createRoot(widgetContainer);
          root.render(React.createElement(ChatWidget, { config }));
        });
      });
    });
  };

  // Load required CSS
  const cssLink = document.createElement('link');
  cssLink.rel = 'stylesheet';
  cssLink.href = '/widget-styles.css'; // This would be the compiled CSS
  document.head.appendChild(cssLink);

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWidget);
  } else {
    initWidget();
  }
};

// Global function that can be called from external scripts
(window as any).initGoBotWidget = createChatWidget;
