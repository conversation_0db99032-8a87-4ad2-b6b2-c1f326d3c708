
import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { store } from '../store/store';
import { X, Plus, Mic, MessageCircle, Menu, ChevronLeft } from 'lucide-react';
import ChatArea from './ChatArea';
import ChatInput from './ChatInput';

const FloatingChatBubble: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const suggestions = [
    { icon: "🖼️", text: "Create image", color: "text-green-600" },
    { icon: "📄", text: "Summarize text", color: "text-orange-600" },
    { icon: "👨‍💻", text: "Code", color: "text-purple-600" },
    { icon: "🎓", text: "Get advice", color: "text-blue-600" },
    { text: "More", color: "text-gray-600" },
  ];

  const chatHistory = [
    { id: 1, title: "GoBot Sales", summary: "Product inquiry discussion", timestamp: "2 hours ago" },
    { id: 2, title: "Technical Support", summary: "API integration help", timestamp: "1 day ago" },
    { id: 3, title: "Feature Request", summary: "New dashboard features", timestamp: "3 days ago" },
    { id: 4, title: "General Questions", summary: "Platform overview", timestamp: "1 week ago" },
  ];

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-2xl flex items-center justify-center transition-all hover:scale-110 relative group animate-bounce"
        >
          <MessageCircle className="w-8 h-8" />

          {/* Notification dot */}
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">1</span>
          </div>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap shadow-lg">
            Chat with AI
            <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </button>
      </div>
    );
  }

  return (
    <Provider store={store}>
      <div className="fixed bottom-6 right-6 z-50">
        <div className="w-96 h-[600px] bg-white rounded-3xl shadow-2xl border border-gray-100 flex overflow-hidden">

          {/* Sidebar */}
          <div className={`${sidebarOpen ? 'w-64' : 'w-0'} transition-all duration-300 bg-gray-50 border-r border-gray-200 flex flex-col overflow-hidden`}>
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Chats</h3>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-1 rounded hover:bg-gray-200 transition-colors"
                >
                  <ChevronLeft className="w-4 h-4 text-gray-500" />
                </button>
              </div>

              <button className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors text-sm">
                <Plus className="w-4 h-4" />
                <span>New Chat</span>
              </button>
            </div>

            {/* Chat History */}
            <div className="flex-1 overflow-y-auto p-2">
              {chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className="p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors mb-2"
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{chat.title}</p>
                    <p className="text-xs text-gray-500 truncate">{chat.summary}</p>
                    <p className="text-xs text-gray-400 mt-1">{chat.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                {!sidebarOpen && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <Menu className="w-5 h-5 text-gray-500" />
                  </button>
                )}
                <div className="w-10 h-10 bg-black rounded-full flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">AI Assistant</h3>
                  <p className="text-xs text-gray-500">Online now</p>
                </div>
              </div>

              <button
                onClick={() => setIsOpen(false)}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                title="Close"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col">
              {/* Welcome Screen */}
              <div className="flex-1 flex flex-col items-center justify-center p-6 bg-gray-50">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    What can I help with?
                  </h2>
                </div>

                {/* Suggestion Buttons */}
                <div className="grid grid-cols-2 gap-3 mb-8 w-full">
                  {suggestions.slice(0, 4).map((suggestion, index) => (
                    <button
                      key={index}
                      className="flex items-center space-x-3 p-4 bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all text-left"
                    >
                      <span className="text-2xl">{suggestion.icon}</span>
                      <span className="text-sm font-medium text-gray-900">
                        {suggestion.text}
                      </span>
                    </button>
                  ))}
                </div>

                {/* More Button */}
                <button className="flex items-center space-x-3 p-3 bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all">
                  <span className="text-gray-600 text-sm font-medium">More</span>
                </button>
              </div>

              {/* Input */}
              <div className="p-4 bg-white border-t border-gray-100">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Ask anything"
                    className="w-full rounded-full border border-gray-200 px-4 py-3 pr-20 focus:border-gray-300 focus:outline-none bg-gray-50 text-gray-900 placeholder-gray-500"
                  />

                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                    <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
                      <Plus className="w-4 h-4 text-gray-500" />
                    </button>

                    <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
                      <Mic className="w-4 h-4 text-gray-500" />
                    </button>

                    <button className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors">
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Provider>
  );
};

export default FloatingChatBubble;
