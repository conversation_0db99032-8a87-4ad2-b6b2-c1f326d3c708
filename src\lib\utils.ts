import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const STRINGS = {
  SUCCESS: "Success.",
  DATA_FETCHED: "Success.",
  COMMON_ERROR: "Something went wrong.",
  TOKEN_INVALID: "Invalid Token.",
  MISSING_TOKEN: "Token not found."
}

export function trim(str: string, ch: string) {
  let start = 0,
    end = str.length;

  while (start < end && str[start] === ch)
    ++start;

  while (end > start && str[end - 1] === ch)
    --end;

  return (start > 0 || end < str.length) ? str.substring(start, end) : str;
}