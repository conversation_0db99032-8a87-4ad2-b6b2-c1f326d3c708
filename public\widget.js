
// GoBot Service Widget Loader
// Usage: <script src="https://your-domain.com/widget.js" data-project-id="your-project-id" data-user-id="your-user-id" data-api-key="your-api-key" data-theme="light"></script>

(function () {
  // Get configuration from script tag attributes
  const script = document.currentScript;
  const config = {
    projectId: script.getAttribute('data-project-id') || '',
    userId: script.getAttribute('data-user-id') || '',
    apiKey: script.getAttribute('data-api-key') || '',
    theme: {
      mode: script.getAttribute('data-theme') || 'light',
      primaryColor: script.getAttribute('data-primary-color') || '#3B82F6',
      backgroundColor: script.getAttribute('data-bg-color') || '#FFFFFF'
    }
  };

  // Load CSS
  const cssLink = document.createElement('link');
  cssLink.rel = 'stylesheet';
  cssLink.href = 'https://cdn.tailwindcss.com';
  document.head.appendChild(cssLink);

  // Load the widget
  function loadWidget() {
    if (window.initGoBotWidget) {
      window.initGoBotWidget(config);
    } else {
      // If the main app isn't loaded yet, wait for it
      setTimeout(loadWidget, 100);
    }
  }

  // Start loading when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadWidget);
  } else {
    loadWidget();
  }
})();
