
import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import LoadingSpinner from './LoadingSpinner';

const BotStatusIndicator: React.FC = () => {
  const botState = useSelector((state: RootState) => state.chat.botState);
  const currentSession = useSelector((state: RootState) => state.auth.currentSession);

  if (botState.status === 'idle') return null;

  const statusConfig = {
    thinking: {
      icon: '🤔',
      text: 'Thinking...',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    },
    writing: {
      icon: '✍️',
      text: 'Writing response...',
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20'
    },
    loading_image: {
      icon: '🖼️',
      text: 'Loading image...',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20'
    },
    chart_generation: {
      icon: '📊',
      text: 'Generating chart...',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20'
    },
    workflow_execution: {
      icon: '⚙️',
      text: 'Executing workflow...',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20'
    }
  };

  const config = statusConfig[botState.status];
  const hasMessages = currentSession && currentSession.messages.length > 0;

  // Show in center if no messages, bottom if there are messages
  if (!hasMessages) {
    return (
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none">
        <div className={`${config.bgColor} rounded-xl p-6 shadow-lg max-w-md mx-4 pointer-events-auto animate-fade-in`}>
          <div className="flex items-center space-x-4 mb-4">
            <span className="text-3xl">{config.icon}</span>
            <div className="flex items-center space-x-2">
              <LoadingSpinner size="sm" />
              <span className={`text-lg font-semibold ${config.color}`}>
                {config.text}
              </span>
            </div>
          </div>

          {botState.thinkingSteps && botState.thinkingSteps.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm text-gray-600 dark:text-gray-400 font-medium mb-2">
                Thinking process:
              </div>
              {botState.thinkingSteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
                  <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
                  <span>{step}</span>
                </div>
              ))}
            </div>
          )}

          {botState.currentMessage && (
            <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current response:</div>
              <div className="text-sm text-gray-900 dark:text-white">
                {botState.currentMessage}
                <span className="inline-block w-2 h-4 bg-current animate-pulse ml-1"></span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Bottom indicator when there are messages
  return (
    <div className="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-2">
        <span className="text-lg">{config.icon}</span>
        <LoadingSpinner size="sm" />
        <span className={`text-sm font-medium ${config.color}`}>
          {config.text}
        </span>
      </div>

      {botState.thinkingSteps && botState.thinkingSteps.length > 0 && (
        <div className="flex-1">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {botState.thinkingSteps[botState.thinkingSteps.length - 1]}
          </div>
        </div>
      )}

      {botState.currentMessage && (
        <div className="flex-1 text-xs text-gray-600 dark:text-gray-400 truncate">
          {botState.currentMessage}
        </div>
      )}
    </div>
  );
};

export default BotStatusIndicator;
