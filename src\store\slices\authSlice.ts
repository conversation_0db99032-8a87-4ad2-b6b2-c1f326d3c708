import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Token, AuthState, Session, ChatSession } from '../../types/chat';
import Cookies from 'js-cookie';


const initialState: AuthState = {
  sessions: [],
  currentSession: null,
  sessionId: Cookies.get('_max_agent_session') || '',
  refreshToken: Cookies.get('_max_agent_refresh_token') || '',
  authToken: Cookies.get('_max_agent_token') || '',
};


const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setToken: (state, action: PayloadAction<Token>) => {
      state.authToken = action.payload.token;
      Cookies.set('_max_agent_token', action.payload.token, { expires: action.payload.exp || new Date(Date.now() + 30 * 60 * 1000), secure: false });
    },
    setRefreshToken: (state, action: PayloadAction<Token>) => {
      state.refreshToken = action.payload.token;
      Cookies.set('_max_agent_refresh_token', action.payload.token, { expires: action.payload.exp || new Date(Date.now() + 30 * 60 * 1000), secure: false });
    },
    setSessionId: (state, action: PayloadAction<string>) => {
      state.sessionId = action.payload;
      Cookies.set('_max_agent_session', action.payload, { expires: 1, secure: false });
    },
    setSessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.sessions = action.payload;
    },
    setCurrentSession: (state, action: PayloadAction<ChatSession | null>) => {
      if (action.payload) {
        state.currentSession = action.payload;
        state.sessionId = action.payload.sessionId;
        if (state.sessions.find(session => session.id === action.payload.id) === undefined) {
          state.sessions.push(action.payload);
        }
        Cookies.set('_max_agent_session', action.payload.sessionId, { expires: new Date(Date.now() + 30 * 60 * 1000), secure: false });
        state.authToken = action.payload.authToken.token;
        Cookies.set('_max_agent_token', state.authToken || '', { expires: new Date(Date.now() + 30 * 60 * 1000), secure: false });
        state.refreshToken = action.payload.refreshToken.token || '';
        Cookies.set('_max_agent_refresh_token', state.refreshToken, { expires: new Date(Date.now() + 30 * 60 * 1000), secure: false });
      }
    },
    addSession: (state, action: PayloadAction<ChatSession>) => {
      state.sessions.push(action.payload);
    },
    deleteSession: (state, action: PayloadAction<string>) => {
      state.sessions = state.sessions.filter(session => session.id !== action.payload);
    },
    clearState: (state) => {
      Cookies.remove('_max_agent_session')
      Cookies.remove('_max_agent_refresh_token')
      Cookies.remove('_max_agent_token')
      state.currentSession = null
      state.sessionId = ''
      state.refreshToken = ''
      state.authToken = ''
    }
  },
});

export const {
  setSessions,
  setToken,
  setRefreshToken,
  setSessionId,
  setCurrentSession,
  addSession,
  deleteSession,
  clearState
} = authSlice.actions;

export default authSlice.reducer;