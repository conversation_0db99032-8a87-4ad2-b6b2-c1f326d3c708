import axios, { AxiosError, AxiosInstance } from 'axios';
import config from '../config';
import { store } from "../store/store";
import { REFRESH_TOKEN } from '../config/endpoints.config';
import { jwtDecode } from 'jwt-decode';
import { Response, Token } from '../types/chat';
import { STRINGS } from '../lib/utils';
import { clearState, setRefreshToken, setToken } from '../store/slices/authSlice';
import { setLoader } from '../store/slices/commonSlice';

class Base {
  protected http: AxiosInstance | null;
  protected baseURL: string;
  private commonHeader: Record<string, string>;

  constructor() {
    if (config.env == 'production') {
      this.commonHeader = {
        "Content-Type": "application/json",
        "Accept": "application/json"
      }

    } else {
      this.commonHeader = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "ngrok-skip-browser-warning": "69420"
      }
    }
    this.http = null
    this.baseURL = config.baseUrl
    this.init()
  }

  protected async refreshToken() {
    try {
      const refreshToken = store.getState().auth.refreshToken;
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      const url = new URL(`${this.baseURL}${REFRESH_TOKEN}`);
      const response = await axios.post(url.toString(), {
        refresh_token: ""
      }, {
        headers: {
          'Authorization': `Bearer ${refreshToken}`
        }
      });

      const res: { status: boolean; data?: { access_token: Token, refresh_token: Token } } = response.data;
      console.log(res)
      if (res?.data?.access_token) {
        console.log(res?.data?.access_token)
        store.dispatch(setToken(res.data.access_token));
      }
      if (res?.data?.refresh_token) {
        console.log(res?.data?.access_token)
        store.dispatch(setRefreshToken(res.data.refresh_token));
      }
      return res;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return { status: false };
    }
  }

  protected isJwtExpired(token: string): boolean {
    try {
      const decoded = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      return (decoded?.exp || 0) < currentTime;
    } catch (error) {
      console.error('Error decoding JWT:', error);
      return true;
    }
  }

  protected getHeaders = async (config: { token?: boolean; customHeaders?: Record<string, string> } = {}): Promise<Record<string, string> | false> => {
    try {
      let headers = { ...this.commonHeader };

      if (config.token) {
        const token = this.getToken();
        if (!token) return this.commonHeader;

        headers['Authorization'] = `Bearer ${token}`;

        if (this.isJwtExpired(token)) {
          const refreshResult = await this.refreshToken();
          if (!refreshResult.status) {
            this._logout();
            return this.commonHeader;
          }
          console.log(refreshResult)
          headers['Authorization'] = `Bearer ${refreshResult.data?.access_token?.token}`;
          console.log(headers)
        }
      }

      if (config.customHeaders) {
        headers = { ...headers, ...config.customHeaders };
      }

      return headers;
    } catch (error) {
      console.error('Error getting headers:', error);
      this._logout();
      return this.commonHeader;
    }
  }

  protected init = async () => {
    this.http = axios.create({
      baseURL: this.baseURL
    })

    // Request interceptors for API calls
    this.http.interceptors.request.use(data => {
      const hideLoader = data?.params?.hideLoader || data?.data?.hideLoader || false;
      // console.log('hideLoader', hideLoader)
      if (hideLoader) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        data?.params?.hideLoader ? delete data.params.hideLoader : null;
        // data?.data?.hideLoader  ? delete data.data.hideLoader : null;
      } else {
        this.showLoader()
      }
      return data;
    },
      error => {
        this.hideLoader()
        return Promise.reject(error);
      }
    );

    //response interceptor
    this.http.interceptors.response.use(response => {
      this.hideLoader();
      return response;
    }, async (error: AxiosError) => {
      this.hideLoader();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const originalRequest = (error.config as any);

      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        if (!originalRequest?._retry) {
          originalRequest._retry = true;
          try {
            const token = await this.refreshToken();
            if (token) {
              return this.http!(originalRequest);
            } else {
              this._logout();
            }
          } catch (_refreshError: unknown) {
            this._logout();
          }
        } else {
          this._logout();
        }
      }
      // if (error.response && error.response.status === 401) {
      //   try {
      //     this._logout()
      //   } catch (err) {
      //     console.log(err)
      //   }
      // }
      return Promise.reject(error)
    });
  }

  getToken() {
    return store.getState().auth?.authToken || ''
  }


  async get(url: string, params: Record<string, unknown> = {}, withToken = true, customHeaders: Record<string, string> = {}) {
    const headers = await this.getHeaders({ token: withToken, customHeaders: customHeaders })
    return this.http?.get(url, {
      params: params,
      headers: headers || {}
    });
  }

  async post(url: string, data: unknown, withToken = true, customHeaders: Record<string, string> = {}) {
    const headers = await this.getHeaders({ token: withToken, customHeaders: customHeaders })

    return this.http?.post(url, data, {
      headers: headers || {}
    });
  }

  async put(url: string, data: unknown, withToken = true, customHeaders: Record<string, string> = {}) {
    const headers = await this.getHeaders({ token: withToken, customHeaders: customHeaders })
    return this.http?.put(url, data, {
      headers: headers || {}
    });
  }

  async delete(url: string, withToken = true, customHeaders: Record<string, string> = {}) {
    const headers = await this.getHeaders({ token: withToken, customHeaders: customHeaders })
    return this.http?.delete(url, {
      headers: headers || {}
    });
  }

  showLoader() {
    store.dispatch(setLoader(true))
  }

  hideLoader() {
    store.dispatch(setLoader(false))
  }

  decode(token: string | null = null) {
    return new Promise(resolve => {
      if (!token) {
        resolve(this.response(false, "Auth token not found"))
      }
      else {
        try {
          const data = jwtDecode(token)
          // data['token'] = token
          resolve(this.response(true, STRINGS.SUCCESS, data))
        } catch (error) {
          resolve(this.response(false, STRINGS.TOKEN_INVALID, error))
        }
      }
    })
  }

  response(status = false, message = STRINGS.COMMON_ERROR, data: null | unknown | Record<string, unknown> = null, errors: null | unknown = null) {
    const res: Response = {
      status,
      message,
      data,
      errors
    }
    return res
  }

  _logout = () => {
    store.dispatch(clearState())
  }
}

export default Base