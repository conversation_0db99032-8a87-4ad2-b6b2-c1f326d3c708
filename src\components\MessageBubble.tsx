
import React from 'react';
import { Message } from '../types/chat';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MessageBubbleProps {
  message: Message;
  isStreaming?: boolean;
  isWidget?: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isStreaming, isWidget }) => {
  const isUser = message.type === 'human';
  const isAssistant = message.type === 'ai';
  const htmlRegex = /<([A-Za-z][A-Za-z0-9]*)\b[^>]*>(.*?)<\/\1>/;
  const content = message.content.replace('### Response', '').replace('###', '').replace('```', '').replace('```', '')
  return (
    <div className={`flex ${isUser ? 'user-chat justify-end' : 'ai-chat justify-start'} ${isWidget ? 'w-full chat-widget' : ''} mb-4`}>
      <div className={`flex items-start space-x-3 max-w-[${isWidget ? 90 : 85}%] ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
        {/* Avatar */}
        <div className={`rounded-full flex items-center justify-center text-white text-sm font-medium avatar ${isUser ? 'bg-blue-600' : 'bg-gray-600'
          }`}>
          {isUser ? 'U' : 'G'}
        </div>

        {/* Message Content */}
        <div className={`rounded-lg px-4 py-2 min-w-0 flex-1 ${isUser
          ? 'bg-blue-600 text-white'
          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
          }`}>
          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mb-2">
              {message.attachments.map((attachment, index) => (
                <div key={index} className="mb-2">
                  {attachment.type.startsWith('image/') ? (
                    <img
                      src={typeof attachment.data === 'string' ? attachment.data : ''}
                      alt={attachment.name}
                      className="max-w-xs rounded-lg"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-2 bg-gray-200 dark:bg-gray-600 rounded">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-sm">{attachment.name}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Message Text whitespace-pre-wrap*/}
          <>
            {htmlRegex.test(content) ? (
              <div className="message-content m-0" dangerouslySetInnerHTML={{ __html: content }}></div>
            ) : (
              <div className="message-content m-0">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    table: ({ children }) => (
                      <div className="table-container">
                        <table>{children}</table>
                      </div>
                    ),
                  }}
                >
                  {content}
                </ReactMarkdown>
                {isStreaming && (
                  <span className="inline-block w-2 h-4 bg-current animate-pulse ml-1 align-text-bottom"></span>
                )}
              </div>
            )}
          </>
          {/* Chart Display */}
          {message.chart && (message?.chart?.url || message?.chart?.summary) ? (
            <div className="mb-2 p-3 bg-white dark:bg-gray-800 rounded-lg border">
              {message.chart.url && (
                <img src={message.chart.url} alt="Chart" className="max-w-full rounded" />
              )}
              {message.chart.summary && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">{message.chart.summary}</p>
              )}
            </div>
          ) : ''}


          {/* Timestamp */}
          <div className={`text-xs mt-1 ${isUser ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
            }`}>
            {new Date(message.timestamp).toLocaleTimeString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
