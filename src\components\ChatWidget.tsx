import React, { useState, useEffect } from 'react';
import { Provider, useDispatch } from 'react-redux';
import { store } from '../store/store';
import { WidgetConfig, ChatSession } from '../types/chat';
import { MessageCircle, X, Maximize2, Minimize2, Plus, ChevronLeft, AlignRight, Trash2 } from 'lucide-react';
import ChatInput from './ChatInput';
import ChatArea from './ChatArea';
import SkeletonLoader from './SkeletonLoader';
import { useChat } from '@/hooks/useChat';
import { ScrollArea } from './ui/scroll-area';
import appConfig from '@/config';

interface ChatWidgetProps {
  config: WidgetConfig;
}

// Internal component that uses hooks
const ChatWidgetContent: React.FC<ChatWidgetProps> = ({ config }) => {
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const {
    currentSession,
    isLoading,
    sessions,
    messages,
    agent,
    userId,
    ipAddress,
    getSessionToken,
    startNewChat,
    deleteSession,
    setCurrentSession
  } = useChat();

  const hasMessages = currentSession && messages.length > 0;
  const suggestions = [
    { icon: "🖼️", text: "Get Company Details", color: "text-green-600" },
    { icon: "💬", text: "Get Product & Service Support", color: "text-orange-600" },
    { icon: "📅", text: "Schedule Sales Demo", color: "text-blue-600" },
    { icon: "📄", text: "Get Sales Report", color: "text-blue-600" }
  ];

  useEffect(() => {
    document.documentElement.className = config.theme.mode;
  }, [config.theme]);

  // Initialize sessions on mount
  useEffect(() => {
    const getSessions = async () => {
      try {
        const { chatService } = await import('@/services/Chat.service');
        await chatService.getSessions(userId, ipAddress);
      } catch (err) {
        console.error('Error getting sessions:', err);
      }
    };
    getSessions();
  }, [userId, ipAddress]);

  const handleDeleteSession = async (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleting(sessionId);
    try {
      await deleteSession(sessionId);
    } finally {
      setIsDeleting(null);
    }

  };

  const handleSessionSelection = async (session: ChatSession) => {
    if (session?.authToken && session?.refreshToken) {
      dispatch(setCurrentSession(session));
    } else {
      await getSessionToken(session);
    }
    setSidebarOpen(false);
  };

  const handleNewChat = async () => {
    const token = config.apiKey;
    const botName = config?.botName || appConfig?.botName || 'GoBot';
    const agentType = config?.agentType || 'service';

    await startNewChat(token, botName, agentType);
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsMinimized(false)}
          className="w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-110 relative group"
        >
          <MessageCircle className="w-8 h-8" />
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat with us!
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className={`fixed z-50 transition-all duration-300 ${isExpanded
      ? 'inset-4'
      : 'bottom-6 right-6 w-96 h-[600px]'
      } bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden`}>

      {/* Widget Header */}
      <div className='border-b border-gray-200 dark:border-gray-700 bg-blue-600 text-white rounded-t-lg flex-shrink-0'>
        <div className="flex items-center justify-between p-4 pb-1">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <MessageCircle className="w-6 h-6" />
            </div>
            <div>
              <h3 className="font-semibold">Chat</h3>
              <p className="text-xs text-blue-100">Questions? Chat with us!</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <AlignRight className="w-5 h-5" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
              title={isExpanded ? "Minimize" : "Expand"}
            >
              {isExpanded ? (
                <Minimize2 className="w-4 h-4" />
              ) : (
                <Maximize2 className="w-4 h-4" />
              )}
            </button>

            <button
              onClick={() => setIsMinimized(true)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
        {/* Status Indicator */}
        <div className="px-4 pb-2">
          <div className="flex items-center space-x-2 ml-3 text-sm text-white-600 dark:text-white-400">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>We're online</span>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex flex-row flex-1 min-h-0">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? isExpanded ? 'w-68' : 'w-70 flex-1' : 'w-0 hidden'} transition-all duration-300 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full min-h-0`}>
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">Chats</h3>
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <ChevronLeft className="w-4 h-4 text-gray-500" />
              </button>
            </div>

            <button
              onClick={handleNewChat}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors text-sm"
            >
              <Plus className="w-4 h-4" />
              <span>New Chat</span>
            </button>
          </div>

          {/* Chat History */}
          <ScrollArea className="flex-1 min-h-0">
            <div className="p-2">
              {isLoading ? (
                <SkeletonLoader type="chat-list" />
              ) : sessions.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <p>No chats yet</p>
                  <p className="text-sm">Start a new conversation</p>
                </div>
              ) : (
                sessions.map((session, index) => (
                  <div
                    key={session.id || index}
                    onClick={async () => await handleSessionSelection(session)}
                    className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors group mb-2 ${currentSession?.id === session.id
                      ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                      }`}
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{(session.title || 'New Chat').length > 35 ? (session.title || 'New Chat').slice(0, 35) + '...' : (session.title || 'New Chat')}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {(session.summary || 'No messages yet').length > 50 ? (session.summary || 'No messages yet').slice(0, 50) + '...' : (session.summary || 'No messages yet')}
                      </p>
                    </div>

                    {isDeleting === session.id ? (
                      <div className="w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-red-600"></div>
                    ) : (
                      <button
                        onClick={async (e) => await handleDeleteSession(session.id, e)}
                        className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/50 transition-all"
                      >
                        <Trash2 className="w-4 h-4 text-red-600" />
                      </button>
                    )}
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
        {/* Chat Content Area */}
        {(isExpanded || !sidebarOpen) && (
          <div className="flex flex-1 flex-col min-h-0">
            {/* Chat area */}
            <div className="flex-1 flex flex-col min-h-0">
              <ChatArea isWidget={!isExpanded} />
            </div>

            {/* Input */}
            <div className={`flex-shrink-0 ${hasMessages ? '' : 'flex justify-center'}`}>
              <div className={hasMessages ? 'w-full' : 'w-full max-w-4xl px-4'}>
                <ChatInput isWidget={!isExpanded} />
              </div>
            </div>
          </div>
        )}

      </div>

      {/* Footer */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Powered by{' '}
          <a href="https://maxmobility.in" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            MaxMobility
          </a>
        </p>
      </div>
    </div>
  );
};

// Main ChatWidget component with Provider
const ChatWidget: React.FC<ChatWidgetProps> = ({ config }) => {
  return (
    <Provider store={store}>
      <ChatWidgetContent config={config} />
    </Provider>
  );
};

export default ChatWidget;
