/* eslint-disable @typescript-eslint/no-explicit-any */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Message, Session, ChatState, SuggestedTools, ChatSession, BotState } from '../../types/chat';

const initialState: ChatState = {
  messages: [],
  pendingMessage: null,
  botState: {
    status: 'idle',
    thinkingSteps: []
  },
  isLoading: false,
  error: null,
  suggestedTools: []
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    add2ndMessage: (state, action: PayloadAction<Message>) => {
      state.messages.splice(1, 0, action.payload);
      state.pendingMessage = null;
    },
    addPendingMessage: (state, action: PayloadAction<Message>) => {
      state.pendingMessage = action.payload;
    },
    extendMessages: (state, action: PayloadAction<Message[]>) => {
      state.messages = [...action.payload, ...state.messages];
    },
    setMessages: (state, action: PayloadAction<Message[]>) => {
      state.messages = action.payload;
    },
    updateMessage: (state, action: PayloadAction<Message>) => {
      const index = state.messages.findIndex(m => m.message_id === action.payload.message_id);
      if (index !== -1) {
        state.messages[index] = action.payload;
      }
    },
    addStreamingMessage: (state, action: PayloadAction<{ message_id: string; content: string }>) => {
      const { message_id, content } = action.payload;
      const existingIndex = state.messages.findIndex(m => m.message_id === message_id);

      if (existingIndex !== -1) {
        // Update existing streaming message
        state.messages[existingIndex].content = content;
        state.messages[existingIndex].isStreaming = true;
      } else {
        // Add new streaming message
        state.messages.push({
          message_id,
          content,
          type: 'ai',
          timestamp: new Date().toISOString(),
          isStreaming: true
        });
      }
    },
    finalizeStreamingMessage: (state, action: PayloadAction<{ message_id: string; content: string; chart?: any }>) => {
      const { message_id, content, chart } = action.payload;
      const index = state.messages.findIndex(m => m.message_id === message_id || m.isStreaming);
      console.log("finalizing message", action.payload)
      if (index !== -1) {
        state.messages[index].content = content;
        state.messages[index].isStreaming = false;
        console.log("Updated message", state.messages[index])
        if (chart) {
          state.messages[index].chart = chart;
        }
      }
    },
    // stopStreamingMessage: (state, action: PayloadAction<string>) => {
    //   const index = state.messages.findIndex(m => m?.isStreaming);
    //   if (index !== -1) {
    //     state.messages[index].isStreaming = false;
    //   }
    // },
    setBotState: (state, action: PayloadAction<BotState>) => {
      state.botState = action.payload;
      if (state.botState.status === 'idle') {
        const index = state.messages.findIndex(m => m?.isStreaming);
        if (index !== -1) {
          state.messages[index].isStreaming = false;
        }
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSuggestedTools: (state, action: PayloadAction<SuggestedTools[]>) => {
      state.suggestedTools = action.payload;
    }
  }
});

export const {
  addMessage,
  add2ndMessage,
  addPendingMessage,
  updateMessage,
  setMessages,
  extendMessages,
  addStreamingMessage,
  finalizeStreamingMessage,
  setSuggestedTools,
  setBotState,
  setLoading,
  setError
} = chatSlice.actions;

export default chatSlice.reducer;