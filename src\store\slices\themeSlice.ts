
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeConfig } from '../../types/chat';

const initialState: ThemeConfig = {
  mode: 'light',
  primaryColor: '#3B82F6',
  backgroundColor: '#FFFFFF'
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.mode = state.mode === 'light' ? 'dark' : 'light';
      state.backgroundColor = state.mode === 'light' ? '#FFFFFF' : '#1F2937';
    },
    setTheme: (state, action: PayloadAction<ThemeConfig>) => {
      return action.payload;
    }
  }
});

export const { toggleTheme, setTheme } = themeSlice.actions;
export default themeSlice.reducer;
