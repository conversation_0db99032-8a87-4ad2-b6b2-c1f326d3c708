import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CommonState {
  isLoading: boolean;
  ipAddress?: string;
  token?: string;
  userId?: string;
  project?: string;
  botName?: string;
  type?: string;
}


const initialState: CommonState = {
  isLoading: false,
  ipAddress: '',
  token: '',
  userId: '',
  project: '',
  botName: '',
  type: ''
};


const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setLoader: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setIpAddress: (state, action: PayloadAction<string>) => {
      state.ipAddress = action.payload;
    },
    setDetails: (state, action: PayloadAction<{ token: string, userId?: string, project?: string, botName?: string, type?: string, ipAddress?: string }>) => {
      state.token = action.payload.token;
      state.userId = action.payload.userId;
      state.project = action.payload.project;
      state.botName = action.payload.botName;
      state.type = action.payload.type;
      state.ipAddress = action.payload.ipAddress;
    }
  },
});

export const {
  setLoader,
  setLoading,
  setDetails,
  setIpAddress
} = commonSlice.actions;

export default commonSlice.reducer;