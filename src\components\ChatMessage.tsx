import React from 'react';
import { User, <PERSON><PERSON> } from 'lucide-react';
import { Message } from '../types/chat';
import ReactMarkdown from 'react-markdown'

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.type === 'human';
  const htmlRegex = /<([A-Za-z][A-Za-z0-9]*)\b[^>]*>(.*?)<\/\1>/
  return (
    <div className={`d-flex mb-3 ${isUser ? 'justify-content-end' : 'justify-content-start'}`}>
      <div
        className={`p-3 rounded ${isUser ? 'bg-primary text-white' : 'bg-light'} chat-message`}
      >
        <div className="d-flex align-items-center mb-1">
          {isUser ? <User size={16} /> : <Bot size={16} />}
          <span className="font-weight-bold ml-2">
            {message.type === 'system' ? 'Agent' : (message.type === 'human' ? 'You' : 'Agent')}
          </span>
        </div>
        {htmlRegex.test(message.content) ? (
          <div className="message-content m-0 overflow-auto max-w-xs" dangerouslySetInnerHTML={{ __html: message.content }}></div>
        ) : (
          <div className="message-content m-0 overflow-auto max-w-xs"> <ReactMarkdown>{message.content}</ReactMarkdown></div>
        )}
      </div>
    </div>
  );
};