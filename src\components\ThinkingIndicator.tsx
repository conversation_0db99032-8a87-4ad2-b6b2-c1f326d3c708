import React from 'react';

interface ThinkingIndicatorProps {
  steps: string[];
}

const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ steps }) => {
  return (
    <div className="pl-10 pr-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-800 max-w-3xl">
      <div className="flex items-center mb-2">
        <div className="flex space-x-1">
          <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse" style={{ animationDelay: '300ms' }}></div>
          <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse" style={{ animationDelay: '600ms' }}></div>
        </div>
        <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Thinking...</span>
      </div>

      {steps.length > 0 && (
        <div className="mt-2 pl-4 border-l-2 border-gray-300 dark:border-gray-600 space-y-1">
          {steps.map((step, index) => (
            <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
              <span className="text-blue-500 dark:text-blue-400">•</span> {step}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ThinkingIndicator;