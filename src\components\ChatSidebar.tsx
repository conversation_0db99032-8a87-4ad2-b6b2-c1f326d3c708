import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import SkeletonLoader from './SkeletonLoader';
import { useChat } from '@/hooks/useChat';
import { PlusCircle, ChevronLeft, Trash2 } from 'lucide-react';
import { ChatSession } from '@/types/chat';
import { ScrollArea } from './ui/scroll-area';

interface ChatSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  chatConfig: { token: string, botName: string, agent: string };
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({ isCollapsed, onToggle, chatConfig }) => {
  const dispatch = useDispatch();
  const { isLoading, sessions, agent, currentSession, getSessionToken, startNewChat, deleteSession, setCurrentSession } = useChat();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const handleDeleteSession = async (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleting(sessionId);
    try {
      await deleteSession(sessionId);
    } finally {
      setIsDeleting(null);
    }
  };

  const handleSessionSelection = async (session: ChatSession) => {
    if (session?.authToken && session?.refreshToken) {
      dispatch(setCurrentSession(session));
    } else {
      await getSessionToken(session);
    }
  };

  const handleNewChat = async () => {
    await startNewChat(chatConfig.token, chatConfig.botName, chatConfig.agent);
  };

  if (isCollapsed) {
    return null;
  }

  return (
    <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full min-h-0">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white capitalize">{chatConfig.botName} {agent.replace('_', ' ')}</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          </div>
        </div>

        <button
          onClick={handleNewChat}
          className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <PlusCircle className="w-4 h-4" />
          <span>New Chat</span>
        </button>
      </div>

      {/* Chat History */}
      <ScrollArea className="flex-1 min-h-0">
        {isLoading ? (
          <SkeletonLoader type="chat-list" />
        ) : (
          <div className="p-2">
            {sessions.length === 0 ? (
              <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                <p>No chats yet</p>
                <p className="text-sm">Start a new conversation</p>
              </div>
            ) : (
              sessions.map((session, index) => (
                <>
                  <div
                    key={session.id || index}
                    onClick={async () => await handleSessionSelection(session)}
                    className={`flex items-center justify-between p-1 py-2 rounded-lg cursor-pointer transition-colors group ${currentSession?.id === session.id
                      ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                      }`}
                  >
                    <div className="flex-1 min-w-0 max-w-[calc(100%-2rem)]">
                      <p className="text-xs font-medium truncate">{(session.title || 'New Chat').length > 35 ? (session.title || 'New Chat').slice(0, 35) + '...' : (session.title || 'New Chat')}</p>
                      <p className="text-gray-500 dark:text-gray-400" style={{ fontSize: '12px' }}>
                        {(session.summary || 'No messages yet').length > 50 ? (session.summary || 'No messages yet').slice(0, 50) + '...' : (session.summary || 'No messages yet')}
                      </p>
                    </div>

                    {isDeleting === session.id ? (
                      <div className="w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-red-600"></div>
                    ) : (
                      <button
                        onClick={async (e) => await handleDeleteSession(session.id, e)}
                        className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/50 transition-all"
                      >
                        <Trash2 className="w-4 h-4 text-red-600" />
                      </button>
                    )}
                  </div>
                  <hr />
                </>
              ))
            )}
          </div>
        )}
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Powered by{' '}
          <a href="https://maxmobility.in" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            MaxMobility
          </a>
        </p>
      </div>
    </div>
  );
};

export default ChatSidebar;
