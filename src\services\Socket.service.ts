import { SocketMessage } from '../types/chat';
import { store } from "../store/store";
import { io, Socket } from "socket.io-client";

export class SocketService {
  private socket: Socket | null = null;
  private messageHandlers: ((data: SocketMessage) => void)[] = [];
  private reconnectTimeout: number | null = null;

  validateConnectionDetails(url: string): boolean {
    const authToken = store.getState().auth?.authToken || '';
    if (!url) {
      console.error("URL is required for socket connection.");
      return false;
    }
    if (!authToken) {
      console.error("Auth token is required for socket connection.");
      return false;
    }
    return true;
  }

  connect(url: string) {
    if (!this.validateConnectionDetails(url)) {
      return null;
    }
    if (this.socket) {
      return this.socket;
    }
    const authTocken = store.getState().auth?.authToken || '';
    // document.cookie = 'X-Authorization=' + (store.getState().chat?.authToken || '') + '; path=/';
    console.log("connection request: " + url)
    this.socket = io(url, {
      transports: ["websocket"],
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000, // 1 second
      auth: {
        token: "Bearer " + authTocken
      },
      extraHeaders: {
        "Authorization": "Bearer " + authTocken,
        "ngrok-skip-browser-warning": "69420"
      }
    });

    this.socket.on("connect", () => {
      console.log("Socket connected", this.socket?.connected);
    });

    this.socket.on("message", (data: SocketMessage) => {
      this.messageHandlers.forEach(handler => handler(data));
    });

    this.socket.on("disconnect", (socket: unknown) => {
      this.socket = null;
      console.log("Socket disconnected", socket);
    });

    this.socket.on("connect_error", (err: Error) => {
      this.socket = null;
      console.log("Socket connection failed: " + err.message || '');
      console.error(err);
    });

    return this.socket;
  }

  emit(event: string, data: string) {
    this.socket?.emit(event, data);
  }

  listen(event: string, handler: (data: unknown) => void) {
    this.socket?.on(event, handler);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  send(data: unknown) {
    if (this.socket) {
      this.socket.emit("message", data);
    }
  }

  removeListener(event: string, handler: (data: unknown) => void) {
    this.socket?.off(event, handler);
  }

  onMessage(handler: (data: SocketMessage) => void) {
    this.messageHandlers.push(handler);
    return () => {
      this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
    };
  }

}