
import React from 'react';

interface SkeletonLoaderProps {
  type: 'message' | 'sidebar' | 'chat-list';
  count?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ type, count = 1 }) => {
  const renderMessageSkeleton = () => (
    <div className="flex items-start space-x-3 p-4 animate-pulse">
      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
      <div className="flex-1">
        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2"></div>
      </div>
    </div>
  );

  const renderSidebarSkeleton = () => (
    <div className="p-4 animate-pulse">
      <div className="h-6 bg-gray-300 rounded w-3/4 mb-4"></div>
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="h-4 bg-gray-300 rounded w-full mb-3"></div>
      ))}
    </div>
  );

  const renderChatListSkeleton = () => (
    <div className="p-2 animate-pulse">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-2 mb-2">
          <div className="w-6 h-6 bg-gray-300 rounded"></div>
          <div className="h-4 bg-gray-300 rounded w-2/3"></div>
        </div>
      ))}
    </div>
  );

  const skeletonMap = {
    message: renderMessageSkeleton,
    sidebar: renderSidebarSkeleton,
    'chat-list': renderChatListSkeleton
  };

  return (
    <div>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>{skeletonMap[type]()}</div>
      ))}
    </div>
  );
};

export default SkeletonLoader;
