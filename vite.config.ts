import path from 'path';
import { defineConfig } from 'vite';
import react from "@vitejs/plugin-react-swc";
// import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: "::",
    port: 8080,
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify('development'), // or 'development' for dev mode
  },
  resolve: {
    preserveSymlinks: true,
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  /*build: {
    lib: {
      entry: resolve(__dirname, 'src/Widget.tsx'),
      name: 'SupportChatWidget',
      fileName: 'support-chat-widget',
      formats: ['es', 'umd', 'cjs'],
    },

    // rollupOptions: {
    //   // Make sure to externalize deps that shouldn't be bundled
    //   external: ['react', 'react-dom'],
    //   output: {
    //     // Provide global variables to use in UMD build
    //     globals: {
    //       'react': 'React',
    //       'react-dom': 'ReactDOM'
    //     }
    //   }
    // },
    // Generate sourcemaps for better debugging
    sourcemap: true,
  }*/
});