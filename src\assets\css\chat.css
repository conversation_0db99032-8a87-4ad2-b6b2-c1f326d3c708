.chat-btn {
    background-color: #007bff;
    border: none;
    padding: 10px;
}

.chat-btn:hover {
    background-color: #0056b3;
}

.container-fluid {
    height: 100vh;
}

.flex-grow-1 {
    flex: 1 1 auto;
}

.overflow-auto {
    overflow-y: auto;
}

.chat-message {
    max-width: 70%;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.ml-2 {
    margin-left: 0.5rem !important;
}

.message-content p {
    margin-bottom: 2px !important;
}


.typing-indicator {
    display: flex;
    padding: 10px;
    background-color: #f0f2f5;
    border-radius: 10px;
    width: 50px;
    justify-content: center;
    margin-bottom: 15px;
    align-self: flex-start;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: #6c757d;
    border-radius: 50%;
    margin: 0 2px;
    animation: bounce 1.5s infinite ease-in-out;
}

.dot:nth-child(1) {
    animation-delay: 0s;
}

.dot:nth-child(2) {
    animation-delay: 0.2s;
}

.dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {

    0%,
    60%,
    100% {
        transform: translateY(0);
    }

    30% {
        transform: translateY(-5px);
    }
}


/* Stylish horizontal scrollbar for suggested tools */
.suggested-tools-container .suggested-tool-card {
    min-width: 141px;
    cursor: pointer;
    border-radius: 50px;
    transition: transform 0.2s ease-in-out;
}

.suggested-tool-card .card-body {
    padding-top: .31rem !important;
    padding-bottom: .31rem !important;
    font-size: 12px;
}

.chat-body,
.suggested-tools-container {
    scrollbar-width: thin;
    /* For Firefox */
    scrollbar-color: #007bff #e9ecef;
}

.chat-body::-webkit-scrollbar,
.suggested-tools-container::-webkit-scrollbar {
    height: 4px;
    /* Scrollbar height */
}

.chat-body::-webkit-scrollbar-track,
.suggested-tools-container::-webkit-scrollbar-track {
    background: #e9ecef;
    /* Track background */
    border-radius: 10px;
}

.chat-body::-webkit-scrollbar-thumb,
.suggested-tools-container::-webkit-scrollbar-thumb {
    background: #007bff;
    /* Thumb color */
    border-radius: 10px;
}

.chat-body::-webkit-scrollbar-thumb:hover,
.suggested-tools-container::-webkit-scrollbar-thumb:hover {
    background: #014b9b;
    /* Thumb hover color */
}

.message-content h4 {
    font-size: 13px;
}

.message-content h3 {
    font-size: 15px;
}

.message-content h2 {
    font-size: 17px;
}

.message-content h1 {
    font-size: 20px;
}

.message-content a {
    color: #fff;
    text-decoration: none;
}

.message-content a {
    color: #fff;
    text-decoration: none;
}

.top-title {
    flex: 1 1 0%;
    margin-left: 105px;
    text-transform: capitalize;
}

.top-title h1 {
    font-weight: 600 !important;
}

.gobot-logo {
    width: 180px;
    height: 'auto';
}

/* .suggested-tool-card:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
} */