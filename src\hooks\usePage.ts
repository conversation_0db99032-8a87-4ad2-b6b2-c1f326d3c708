import { useDispatch, useSelector } from 'react-redux';
import { chatService } from '../services/Chat.service';
import { setMessages, extendMessages } from '../store/slices/chatSlice';
import { ChatSession } from '../types/chat';
import { useState, useEffect, useRef } from 'react';

export const usePage = ({ currentSession }: { currentSession: ChatSession }) => {
    const dispatch = useDispatch();
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);

    useEffect(() => {
        if (currentSession?.id) {
            chatService.getMessages(currentSession.id, 10, page)
                .then(response => {
                    if (!response || response.length === 0) {
                        setHasMore(false);
                    } else {
                        if (page > 1) {
                            dispatch(extendMessages(response));
                        } else {
                            dispatch(setMessages(response));
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching messages:', error);
                    setHasMore(false);
                });
        }
    }, [currentSession?.id, page, dispatch]);

    return {
        hasMore,
        page,
        setPage,
    }
}