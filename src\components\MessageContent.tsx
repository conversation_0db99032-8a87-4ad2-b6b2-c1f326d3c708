import React from 'react'
import { Message } from '../types/chat'
import { Paperclip } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm';

interface ChatMessageProps {
    message: Message;
    agent: string;
}

const MessageContent: React.FC<ChatMessageProps> = ({ message, agent }) => {
    return (
        <div
            className={`message ${message.type}-message`}
        >
            <div className="message-container">
                <div className="message-title">
                    {message.type == 'human' ? 'You' : agent}
                </div>
                {/* {htmlRegex.test(message.content) ? (
                    <div className="message-content m-0" dangerouslySetInnerHTML={{ __html: message.content }}></div>
                ) : ( */}
                {/* <div className="message-content m-0"> <ReactMarkdown>{message.content.replace('### Response', '').replace('###', '')}</ReactMarkdown></div> */}
                <div className="message-content m-0"> <ReactMarkdown
                    remarkPlugins={[remarkGfm]}>{message.content.replace('### Response', '').replace('###', '').replace('```', '').replace('```', '')}</ReactMarkdown></div>

                {message?.chart && message.chart?.url ? <div className='image-container'>
                    <img className='img-fluid' src={message.chart.url}></img>
                </div> : null}
                {/* )} */}
                {message.attachments && message.attachments.length > 0 && (
                    <div className="message-attachments">
                        {message.attachments.map((file, index) => (
                            <div key={index} className="message-attachment">
                                <Paperclip size={14} />
                                <span>{file.name}</span>
                            </div>
                        ))}
                    </div>
                )}
            </div>
            <div className="message-timestamp">
                {(typeof message.timestamp === 'string' ? new Date(parseInt(message.timestamp)) : message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
        </div>
    )
}

export default MessageContent