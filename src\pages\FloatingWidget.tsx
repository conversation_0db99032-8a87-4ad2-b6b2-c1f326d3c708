
import React from 'react';
import FloatingChatWidget from '../components/FloatingChatWidget';

const FloatingWidget: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Floating Chat Widget Demo
        </h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Demo Page Content
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            This is a demo page showing the floating chat widget. The widget appears as a floating 
            chat bubble in the bottom-right corner and expands to show the chat interface when clicked.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Features</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Floating chat bubble</li>
                <li>• Suggestion buttons</li>
                <li>• Clean, modern design</li>
                <li>• Responsive layout</li>
              </ul>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Interactions</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Click bubble to open</li>
                <li>• Use suggestion buttons</li>
                <li>• Type in the input field</li>
                <li>• Minimize when done</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating Chat Widget */}
      <FloatingChatWidget />
    </div>
  );
};

export default FloatingWidget;
