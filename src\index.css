@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Prevent body scroll for chat pages */
  html,
  body {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  #root {
    height: 100%;
    width: 100%;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Animation classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Widget styles for external embedding */
#gobot-widget-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

#gobot-widget-container * {
  box-sizing: border-box;
}

.message-content {
  overflow-x: visible;
  /* Changed from auto to visible to allow table container to handle overflow */
  word-wrap: break-word;
  max-width: 100%;
  min-width: 0;
  /* Allow shrinking */
}

.message-content p,
.message-content ul li,
.message-content ol li {
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}

.message-content h4 {
  font-size: 13px;
}

.message-content h3 {
  font-size: 14px;
}

.message-content h2 {
  font-size: 15x;
}

.message-content h1 {
  font-size: 17px;
}

.user-chat .message-content a {
  color: #fff;
  text-decoration: none;
}

.user-chat .avatar,
.ai-chat .avatar {
  max-width: 32px;
  width: 100px;
  height: 32px;
}

.chat-widget .avatar {
  max-width: 24px;
  width: 100px;
  height: 24px;
  font-size: 12px;
}

/* Table container with horizontal scroll */
.message-content .table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  /* Made border more visible for debugging */
  max-width: calc(100vw - 120px);
  /* Ensure it doesn't exceed viewport */
  min-width: 0;
  /* Allow shrinking */
  position: relative;
  /* Ensure scrollbar is positioned correctly */
  min-height: 50px;
  /* Minimum height to ensure scrollbar is visible */
}

.message-content table {
  width: 100%;
  min-width: 400px;
  /* Increased min-width to force horizontal scroll */
  border-collapse: collapse;
  font-size: 12px;
  background-color: transparent;
  table-layout: auto;
  /* Allow table to size naturally */
}

.message-content table thead th {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 0.5rem;
  text-align: left;
  text-transform: capitalize;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.message-content table tbody td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: left;
  white-space: nowrap;
}

.message-content table tbody tr:last-child td {
  border-bottom: none;
}

.message-content table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dark mode table styles */
.dark .message-content table thead th {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.05);
}

.dark .message-content table tbody td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .message-content table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* AI message table styles (light background) */
.ai-chat .message-content table thead th {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 0, 0, 0.05);
  color: inherit;
}

.ai-chat .message-content table tbody td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: inherit;
}

.ai-chat .message-content table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* User message table styles (blue background) */
.user-chat .message-content table thead th {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.user-chat .message-content table tbody td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.user-chat .message-content table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Custom scrollbar for table container */
.message-content .table-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.5) rgba(255, 255, 255, 0.1);
}

.message-content .table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.message-content .table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.message-content .table-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-content .table-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.7);
}

.message-content .table-container::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* AI message table scrollbar */
.ai-chat .message-content .table-container {
  scrollbar-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(0, 0, 0, 0.2);
  /* Proper border for AI messages */
}

.ai-chat .message-content .table-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.ai-chat .message-content .table-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.ai-chat .message-content .table-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.6);
}

.ai-chat .message-content .table-container::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.1);
}

/* Mobile responsive table styles */
@media (max-width: 768px) {
  .message-content .table-container {
    font-size: 11px;
  }

  .message-content table {
    min-width: 250px;
  }

  .message-content table thead th,
  .message-content table tbody td {
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 480px) {
  .message-content .table-container {
    font-size: 10px;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .message-content table {
    min-width: 200px;
  }

  .message-content table thead th,
  .message-content table tbody td {
    padding: 0.4rem 0.2rem;
  }
}