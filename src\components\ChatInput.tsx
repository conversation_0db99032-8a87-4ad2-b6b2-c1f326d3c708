import React, { useState, useRef } from 'react';
import { useChat } from '../hooks/useChat';
import { useSpeech } from '../hooks/useSpeech';
import { ArrowUp, StopCircle } from 'lucide-react';

const ChatInput: React.FC<{ isWidget?: boolean }> = ({ isWidget }) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { sendMessage, currentSession, messages, botState, token, agent } = useChat();
  const { isListening, startListening } = useSpeech();
  const isTyping = botState.status !== 'idle';


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() && attachments.length === 0) return;

    const attachmentData = attachments.map(file => ({
      id: Math.random().toString(36),
      type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size
    }));

    await sendMessage(message, attachmentData);
    setMessage('');
    setAttachments([]);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleVoiceInput = () => {
    startListening((text) => {
      setMessage(prev => prev + text);
    });
  };

  // Position input based on whether there are messages
  const hasMessages = currentSession && messages.length > 0;
  const inputClasses = hasMessages
    ? "border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800"
    : "p-4 bg-gray dark:bg-dark-800";

  // const containerClasses = hasMessages
  //   ? ""
  //   : "flex justify-center items-end min-h-0";

  return (
    <div className={inputClasses}>
      <div className={`flex justify-center items-end min-h-0`}>
        <div className={`w-full max-w-4xl`}>
          {/* Attachments Preview */}
          {attachments.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                    <button
                      onClick={() => removeAttachment(index)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Input Form */}
          <form onSubmit={handleSubmit} className="flex items-end space-x-3">
            <div className="flex-1">
              <div className="relative">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={hasMessages ? "Message GoBot Service..." : "Message GoBot Service..."}
                  className="w-full resize-none rounded-xl border border-gray-300 dark:border-gray-600 px-4 py-3 pr-20 focus:border-blue-500 focus:outline-none dark:bg-gray-700 dark:text-white shadow-sm"
                  rows={1}
                  style={{ minHeight: '48px', maxHeight: '120px' }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />

                {/* Action Buttons */}
                <div className="absolute right-2 bottom-2 flex items-center space-x-1">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,.pdf,.doc,.docx,.txt"
                    onChange={handleFileSelect}
                    className="hidden"
                  />

                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    title="Attach file"
                  >
                    <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                  </button>

                  <button
                    type="button"
                    onClick={handleVoiceInput}
                    disabled={isListening}
                    className={`p-1.5 rounded-lg transition-colors ${isListening
                      ? 'bg-red-100 text-red-600 dark:bg-red-900/50'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-500'
                      }`}
                    title={isListening ? 'Listening...' : 'Voice input'}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>

                  <button
                    type="submit"
                    className={`bg-gray-900 hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-white p-3 rounded-full transition-colors min-w-[35px] h-[35px] flex items-center justify-center ${!message.trim() && attachments.length === 0 ? 'hidden' : ''}`}
                  >
                    {isTyping ? (
                      <StopCircle className="w-4 h-4" />
                    ) : (
                      <ArrowUp className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
