.support-chat-widget-container {
    height: 100vh;
    width: 100vw;
}

.support-chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.chat-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    right: 0;
    transition: all 0.3s ease;
}

/* .chat-button.open {
    background-color: #dc3545;
} */

.chat-window {
    position: absolute;
    bottom: 65px;
    right: 0;
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.chat-window.open {
    transform: scale(1);
}

.support-chat-widget.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 20px;
    right: 20px;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    bottom: auto;
    right: auto;
    z-index: 10000;
}

.chat-header {
    background-color: #007bff;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-title {
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
}

.close-button,
.fullscreen-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
}


.chat-body {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
}

@media (max-width: 480px) {
    .chat-window {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }
}


.welcome-message {
    text-align: center;
    padding: 20px 0;
    color: #6c757d;
}

.messages-container {
    display: flex;
    flex-direction: column;
}

.message-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #3c4146;
    margin-top: 5px;
    align-self: flex-start;
    margin-left: 2px;
    margin-bottom: 2px;
}

.message {
    margin-bottom: 15px;
    max-width: 80%;
    display: flex;
    flex-direction: column;
}

.human-message {
    align-self: flex-end;
}

.ai-message {
    align-self: flex-start;
}

.message-content {
    padding: 10px;
    border-radius: 10px;
    word-break: break-word;
    font-size: 12px;
    overflow-x: hidden;
    /* Prevent overflow from message content */
    max-width: 100%;
    min-width: 0;
    /* Allow shrinking */
    box-sizing: border-box;
    /* Include padding in width calculation */
}

.human-message .message-content {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 0;
}

.ai-message .message-content {
    background-color: #f0f2f5;
    color: #1c1e21;
    border-bottom-left-radius: 0;
}

.message-timestamp {
    font-size: 0.7rem;
    color: #6c757d;
    margin-top: 3px;
    align-self: flex-end;
    font-size: 11px;
    margin-left: 2px;
}


.ai-message .message-timestamp {
    align-self: flex-start;
}

.typing-indicator {
    display: flex;
    padding: 10px;
    background-color: #f0f2f5;
    border-radius: 10px;
    width: 50px;
    justify-content: center;
    margin-bottom: 15px;
    align-self: flex-start;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: #6c757d;
    border-radius: 50%;
    margin: 0 2px;
    animation: bounce 1.5s infinite ease-in-out;
}

.dot:nth-child(1) {
    animation-delay: 0s;
}

.dot:nth-child(2) {
    animation-delay: 0.2s;
}

.dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {

    0%,
    60%,
    100% {
        transform: translateY(0);
    }

    30% {
        transform: translateY(-5px);
    }
}

.chat-footer {
    padding: 10px;
    border-top: 1px solid #e9ecef;
}

.attachment-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.attachment-preview {
    display: flex;
    align-items: center;
    background-color: #f0f2f5;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.85rem;
}

.attachment-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.message-input-container {
    display: flex;
    position: relative;
}

.message-input {
    flex: 1;
    padding: 8px 60px 10px 15px;
    border-radius: 20px;
    border: 1px solid #c7c7c7;
    outline: none;
    resize: none;
    font-size: 14px;
}

.message-actions {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
}

.attachment-button,
.send-button,
.new-chat,
.stop-button {
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 5px;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button {
    color: #007bff;
}

.new-chat {
    color: #007bff;
}

.stop-button {
    color: #bd0c0c !important;
}

.send-button:disabled {
    color: #6c757d;
    opacity: 0.5;
    cursor: not-allowed;
}

.message-attachments {
    margin-top: 5px;
}

.message-attachment {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    margin-top: 2px;
}

/* Mobile responsive styles */
@media (max-width: 480px) {
    .chat-window {
        width: 300px;
        height: 400px;
        bottom: 60px;
    }
}

/* Stylish horizontal scrollbar for suggested tools */
.suggested-tools-container .suggested-tool-card {
    min-width: 141px;
    cursor: pointer;
    border-radius: 50px;
    transition: transform 0.2s ease-in-out;
}

.suggested-tool-card .card-body {
    padding-top: .2rem !important;
    padding-bottom: .18rem !important;
    font-size: 12px;
}

.powered-by {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    margin-top: 10px;
}

.chat-body,
.suggested-tools-container {
    scrollbar-width: thin;
    /* For Firefox */
    scrollbar-color: #007bff #e9ecef;
}

.chat-body::-webkit-scrollbar,
.suggested-tools-container::-webkit-scrollbar {
    height: 2px;
    /* Scrollbar height */
}

.chat-body::-webkit-scrollbar-track,
.suggested-tools-container::-webkit-scrollbar-track {
    background: #e9ecef;
    /* Track background */
    border-radius: 10px;
}

.chat-body::-webkit-scrollbar-thumb,
.suggested-tools-container::-webkit-scrollbar-thumb {
    background: #007bff;
    /* Thumb color */
    border-radius: 10px;
}

.chat-body::-webkit-scrollbar-thumb:hover,
.suggested-tools-container::-webkit-scrollbar-thumb:hover {
    background: #014b9b;
    /* Thumb hover color */
}

.message-content h4 {
    font-size: 13px;
}

.message-content h3 {
    font-size: 15px;
}

.message-content h2 {
    font-size: 17px;
}

.message-content h1 {
    font-size: 20px;
}

.message-content a {
    color: #fff;
    text-decoration: none;
}

.top-title {
    flex: 1 1 0%;
    margin-left: 105px;
    text-transform: capitalize;
}

.top-title h1 {
    font-weight: 600 !important;
}

.gobot-logo {
    width: 180px;
    height: 'auto';
}

/* .suggested-tool-card:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
} */